# 自定义生命表整合方案

## 概述

本文档描述了如何将 `load_custom_life_table` 方法与 `data_loader.py` 整合，实现统一的数据加载和处理机制。

## 整合架构

### 🏗️ **架构层次**

```
ColorectalCancerMicrosimulationModel (主模型)
    ↓
PopulationModule (人口模块)
    ↓
LifeTable (生命表类) + DataLoader (数据加载器)
```

### 📁 **文件结构**

```
src/ccsm/
├── core/
│   └── model.py                 # 主模型类，提供统一接口
├── modules/
│   └── population.py            # 人口模块，包含LifeTable类
└── data/
    ├── data_loader.py           # 数据加载器，统一数据处理
    └── data_manager.py          # 数据管理器
```

## 实现细节

### 1. **DataLoader 新增方法**

```python
def load_custom_lifetable(self, file_path: str) -> Dict[str, Dict[int, float]]:
    """从自定义文件加载生命表数据"""
    # 支持 CSV 和 Excel 格式
    # 使用统一的数据处理逻辑
    # 提供详细的错误处理和用户提示
```

**特性：**
- ✅ 支持多种文件格式 (.xlsx, .xls, .csv)
- ✅ 智能列名识别
- ✅ 数据验证和错误处理
- ✅ 详细的加载反馈

### 2. **LifeTable 类更新**

```python
def load_custom_life_table(self, file_path: str):
    """从文件加载自定义生命表"""
    # 使用 DataLoader 的统一处理逻辑
    # 更新内部死亡率数据结构
```

**改进：**
- ✅ 复用 DataLoader 的处理逻辑
- ✅ 统一的错误处理
- ✅ 数据格式验证

### 3. **PopulationModule 新增方法**

```python
def load_custom_life_table(self, file_path: str) -> bool:
    """为人口模块加载自定义生命表"""
    # 使用 DataLoader 加载数据
    # 更新 LifeTable 对象
    # 更新缓存数据
```

**功能：**
- ✅ 统一的数据加载入口
- ✅ 自动更新相关组件
- ✅ 返回加载状态

### 4. **主模型类便捷接口**

```python
def load_custom_life_table(self, file_path: str) -> bool:
    """加载自定义生命表数据"""
    return self.population_module.load_custom_life_table(file_path)
```

## 使用方法

### 🚀 **基本使用**

```python
from src.ccsm.core.model import ColorectalCancerMicrosimulationModel

# 创建模型
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 加载自定义生命表
success = model.load_custom_life_table("path/to/custom_lifetable.xlsx")

if success:
    # 设置人口并运行模拟
    model.setup_population(age_distribution, gender_ratio=0.5)
    results = model.run_simulation(years=20, screening_strategy="annual_fit")
```

### 📊 **支持的文件格式**

#### Excel 格式 (.xlsx, .xls)
```
| age | male_mortality | female_mortality | total_mortality |
|-----|----------------|------------------|-----------------|
| 0   | 0.007         | 0.006           | 0.0065         |
| 1   | 0.0005        | 0.0004          | 0.00045        |
| 5   | 0.0002        | 0.0001          | 0.00015        |
```

#### CSV 格式 (.csv)
```csv
age,male_mortality,female_mortality,total_mortality
0,0.007,0.006,0.0065
1,0.0005,0.0004,0.00045
5,0.0002,0.0001,0.00015
```

### 🏷️ **支持的列名**

| 数据类型 | 支持的列名 |
|----------|------------|
| 年龄列 | `age`, `年龄`, `Age`, `x` |
| 男性死亡率 | `male`, `男`, `qx_m`, `mx_m` |
| 女性死亡率 | `female`, `女`, `qx_f`, `mx_f` |
| 总体死亡率 | `total`, `qx`, `mx`, `死亡率` |

## 错误处理

### 🛡️ **自动降级机制**

1. **文件不存在** → 使用默认生命表数据
2. **格式不支持** → 提示支持的格式并使用默认数据
3. **数据解析失败** → 提供格式要求说明并使用默认数据
4. **数据验证失败** → 使用部分有效数据或默认数据

### 📝 **详细的用户反馈**

```python
# 成功加载
✅ 成功加载自定义生命表: custom_lifetable.xlsx
   - 男性数据: 21 个年龄组
   - 女性数据: 21 个年龄组
   - 总体数据: 21 个年龄组

# 加载失败
❌ 加载自定义生命表失败: 文件不存在
💡 请确保文件格式正确，包含年龄列和死亡率列
💡 支持的列名示例:
   - 年龄列: 'age', '年龄', 'Age', 'x'
   - 男性死亡率: 'male', '男', 'qx_m', 'mx_m'
   - 女性死亡率: 'female', '女', 'qx_f', 'mx_f'
   - 总体死亡率: 'total', 'qx', 'mx', '死亡率'
```

## 优势

### 🎯 **统一性**
- 所有数据加载都通过 DataLoader 处理
- 统一的错误处理和用户反馈
- 一致的数据格式和验证逻辑

### 🔧 **可维护性**
- 数据处理逻辑集中在 DataLoader
- 各模块职责清晰
- 易于扩展和修改

### 👥 **用户友好性**
- 简单的 API 接口
- 详细的错误提示
- 自动格式识别

### 🚀 **性能优化**
- 数据缓存机制
- 避免重复加载
- 智能数据处理

## 扩展性

### 🔮 **未来扩展方向**

1. **支持更多数据源**
   - 数据库连接
   - API 数据获取
   - 在线数据源

2. **增强数据验证**
   - 数据质量检查
   - 异常值检测
   - 数据完整性验证

3. **可视化支持**
   - 生命表数据可视化
   - 加载过程可视化
   - 数据质量报告

## 示例代码

完整的使用示例请参考：
- `examples/custom_lifetable_example.py` - 基本使用示例
- `examples/data_integrated_simulation.py` - 集成数据模拟示例

## 总结

通过这种整合方案，我们实现了：

1. ✅ **统一的数据加载机制** - 所有数据通过 DataLoader 处理
2. ✅ **简洁的用户接口** - 主模型提供一键加载功能
3. ✅ **强大的错误处理** - 自动降级和详细反馈
4. ✅ **良好的扩展性** - 易于添加新的数据源和格式
5. ✅ **完整的文档和示例** - 便于用户理解和使用

这种设计确保了代码的可维护性、用户体验和系统的健壮性。
