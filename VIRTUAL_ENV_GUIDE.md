# 虚拟环境使用指南

## 概述

本项目已经创建了一个完整的Python虚拟环境，位于 `.env` 文件夹中。该虚拟环境包含了运行结直肠癌筛查微观模拟模型所需的所有依赖包。

## 已安装的包

虚拟环境中已安装以下主要包：

### 核心依赖
- **numpy** (2.3.2) - 科学计算基础库
- **scipy** (1.16.1) - 科学计算扩展库
- **pandas** (2.3.1) - 数据处理和分析
- **scikit-learn** (1.7.1) - 机器学习库
- **matplotlib** (3.10.3) - 数据可视化
- **tqdm** (4.67.1) - 进度条显示

### 测试工具
- **pytest** (8.4.1) - 测试框架

### 项目本身
- **ccsm** (1.0.0) - 结直肠癌筛查微观模拟模型（以开发模式安装）

## 如何激活虚拟环境

### 方法1：使用提供的脚本（推荐）

#### Windows 命令提示符 (CMD)
```cmd
activate_env.bat
```

#### PowerShell
```powershell
.\activate_env.ps1
```

### 方法2：手动激活

#### Windows 命令提示符 (CMD)
```cmd
.env\Scripts\activate.bat
```

#### PowerShell
```powershell
.env\Scripts\Activate.ps1
```

## 验证安装

激活虚拟环境后，可以运行以下命令验证安装：

```bash
# 查看已安装的包
pip list

# 运行简单测试
python simple_test.py

# 运行基本模拟示例
python examples\basic_simulation.py

# 运行数据集成模拟示例
python examples\data_integrated_simulation.py

# 运行所有测试
python -m pytest
```

## 注意事项

1. **TensorFlow未安装**：由于当前Python版本(3.13)较新，TensorFlow暂不支持。项目已配置为在没有TensorFlow的情况下使用简化的校准方法。

2. **虚拟环境位置**：虚拟环境位于项目根目录的 `.env` 文件夹中。

3. **开发模式安装**：项目以开发模式（-e）安装，这意味着对源代码的修改会立即生效，无需重新安装。

## 常见问题

### Q: PowerShell提示执行策略错误
A: 这是Windows的安全设置。可以使用以下命令临时允许脚本执行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Q: 如何退出虚拟环境？
A: 在激活的虚拟环境中运行：
```bash
deactivate
```

### Q: 如何重新创建虚拟环境？
A: 删除 `.env` 文件夹，然后重新运行：
```bash
python -m venv .env
.env\Scripts\python.exe -m pip install --upgrade pip
.env\Scripts\python.exe -m pip install -r requirements.txt
.env\Scripts\python.exe -m pip install -e .
```

## 项目结构

```
项目根目录/
├── .env/                    # 虚拟环境文件夹
│   ├── Scripts/            # 可执行文件和激活脚本
│   ├── Lib/               # Python库文件
│   └── pyvenv.cfg         # 虚拟环境配置
├── src/                   # 项目源代码
├── examples/              # 示例代码
├── requirements.txt       # 依赖包列表
├── setup.py              # 项目安装配置
├── activate_env.bat       # Windows CMD激活脚本
├── activate_env.ps1       # PowerShell激活脚本
└── VIRTUAL_ENV_GUIDE.md   # 本指南文件
```
