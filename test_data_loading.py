#!/usr/bin/env python3
"""
数据加载测试脚本
测试Excel数据文件的加载和处理功能
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===\n")
    
    try:
        from src.ccsm.data.data_loader import DataLoader
        
        # 创建数据加载器
        loader = DataLoader()
        print("✅ 数据加载器创建成功")
        
        # 测试基准数据加载
        print("\n1. 测试基准数据加载...")
        benchmark_data = loader.load_benchmark_data()
        
        print(f"   基准数据包含 {len(benchmark_data)} 个数据组:")
        for key, value in benchmark_data.items():
            if isinstance(value, dict):
                print(f"   - {key}: {len(value)} 个子项")
            else:
                print(f"   - {key}: {type(value).__name__}")
        
        # 测试寿命表数据加载
        print("\n2. 测试寿命表数据加载...")
        lifetable_data = loader.load_lifetable_data()
        
        print(f"   寿命表数据包含:")
        for gender, data in lifetable_data.items():
            print(f"   - {gender}: {len(data)} 个年龄点")
            if data:
                ages = sorted(data.keys())
                print(f"     年龄范围: {min(ages)}-{max(ages)}")
                print(f"     示例数据: 50岁死亡率 = {data.get(50, 'N/A')}")
        
        # 测试人口构成数据加载
        print("\n3. 测试人口构成数据加载...")
        population_data = loader.load_population_data()
        
        print(f"   人口构成数据:")
        print(f"   - 年龄分布: {len(population_data['age_distribution'])} 个年龄组")
        print(f"   - 性别比例: {population_data['gender_ratio']:.3f}")
        print(f"   - 总人口: {population_data['total_population']:,}")
        
        if population_data['age_distribution']:
            ages = sorted(population_data['age_distribution'].keys())
            print(f"   - 年龄范围: {min(ages)}-{max(ages)}")
            
            # 显示年龄分布
            print("   - 年龄分布详情:")
            for age in sorted(ages)[:5]:  # 显示前5个年龄组
                prop = population_data['age_distribution'][age]
                print(f"     {age}岁: {prop:.3f} ({prop*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_population_module_with_data():
    """测试人口模块使用实际数据"""
    print("\n=== 测试人口模块使用实际数据 ===\n")
    
    try:
        from src.ccsm.modules.population import PopulationModule
        
        # 创建人口模块
        pop_module = PopulationModule(initial_population=1000)
        print("✅ 人口模块创建成功")
        
        # 初始化人口（使用实际数据）
        population = pop_module.initialize_population()
        
        print(f"✅ 人口初始化成功: {len(population)} 个个体")
        
        # 分析人口统计
        ages = [ind.current_age for ind in population]
        genders = [ind.gender.name for ind in population]
        
        print(f"   年龄统计:")
        print(f"   - 最小年龄: {min(ages)}")
        print(f"   - 最大年龄: {max(ages)}")
        print(f"   - 平均年龄: {sum(ages)/len(ages):.1f}")
        
        male_count = sum(1 for g in genders if g == 'MALE')
        female_count = len(genders) - male_count
        print(f"   性别统计:")
        print(f"   - 男性: {male_count} ({male_count/len(genders)*100:.1f}%)")
        print(f"   - 女性: {female_count} ({female_count/len(genders)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 人口模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_disease_module_with_data():
    """测试疾病模块使用实际数据"""
    print("\n=== 测试疾病模块使用实际数据 ===\n")
    
    try:
        from src.ccsm.modules.disease import DiseaseNaturalHistoryModule
        
        # 创建疾病模块
        disease_module = DiseaseNaturalHistoryModule()
        print("✅ 疾病模块创建成功")
        
        # 检查参数是否已调整
        print(f"   疾病参数:")
        print(f"   - 腺瘤生成概率参数a: {disease_module.params.adenoma_generation_a:.6f}")
        print(f"   - 癌症进展概率: {disease_module.params.cancer_progression_prob:.6f}")
        print(f"   - 临床前癌症停留时间: {disease_module.params.dwell_time_mean:.1f}年")
        
        return True
        
    except Exception as e:
        print(f"❌ 疾病模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_calibration_module_with_data():
    """测试校准模块使用实际数据"""
    print("\n=== 测试校准模块使用实际数据 ===\n")
    
    try:
        from src.ccsm.modules.calibration import CalibrationModule
        
        # 创建校准模块
        calibration_module = CalibrationModule()
        print("✅ 校准模块创建成功")
        
        # 检查校准目标
        print(f"   校准目标数量: {len(calibration_module.calibration_targets)}")
        
        for i, target in enumerate(calibration_module.calibration_targets[:3]):  # 显示前3个目标
            print(f"   目标 {i+1}: {target.name}")
            print(f"   - 性别: {target.gender}")
            print(f"   - 年龄组数: {len(target.age_groups)}")
            print(f"   - 权重: {target.weight}")
        
        return True
        
    except Exception as e:
        print(f"❌ 校准模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始数据加载功能测试...\n")
    
    tests = [
        test_data_loader,
        test_population_module_with_data,
        test_disease_module_with_data,
        test_calibration_module_with_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有数据加载功能测试通过！")
        print("\n📊 数据文件已成功集成到CCSM模型中:")
        print("   • default_benchmarks.xlsx - 校准基准值")
        print("   • lifetable.xlsx - 寿命表数据")
        print("   • population_Nanshan.xlsx - 南山区人口构成")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块和数据文件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
