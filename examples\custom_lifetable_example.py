"""
自定义生命表加载示例
演示如何使用CCSM模型加载自定义生命表数据
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.data.data_loader import DataLoader


def create_sample_lifetable_file():
    """创建示例生命表文件"""
    # 创建示例数据
    ages = list(range(0, 101, 5))  # 每5岁一个年龄组
    
    data = []
    for age in ages:
        # 简化的死亡率计算（实际应使用真实数据）
        if age < 1:
            male_rate = 0.007
            female_rate = 0.006
        elif age < 15:
            male_rate = 0.0002
            female_rate = 0.0001
        elif age < 45:
            male_rate = 0.001 + age * 0.00005
            female_rate = 0.0005 + age * 0.00003
        elif age < 65:
            male_rate = 0.005 + (age - 45) * 0.0002
            female_rate = 0.003 + (age - 45) * 0.00015
        else:
            male_rate = 0.01 + (age - 65) * 0.002
            female_rate = 0.007 + (age - 65) * 0.0015
        
        data.append({
            'age': age,
            'male_mortality': male_rate,
            'female_mortality': female_rate,
            'total_mortality': (male_rate + female_rate) / 2
        })
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    excel_path = project_root / "custom_lifetable.xlsx"
    df.to_excel(excel_path, index=False)
    
    # 保存为CSV文件
    csv_path = project_root / "custom_lifetable.csv"
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建示例生命表文件:")
    print(f"   - Excel格式: {excel_path}")
    print(f"   - CSV格式: {csv_path}")
    
    return excel_path, csv_path


def demonstrate_lifetable_loading():
    """演示生命表加载功能"""
    print("=== 自定义生命表加载示例 ===\n")
    
    # 1. 创建示例生命表文件
    print("1. 创建示例生命表文件...")
    excel_path, csv_path = create_sample_lifetable_file()
    
    # 2. 直接使用DataLoader加载
    print("\n2. 使用DataLoader直接加载生命表...")
    data_loader = DataLoader()
    
    # 加载Excel格式
    lifetable_data_excel = data_loader.load_custom_lifetable(str(excel_path))
    print(f"   Excel数据加载结果: {len(lifetable_data_excel)} 个性别组")
    
    # 加载CSV格式
    lifetable_data_csv = data_loader.load_custom_lifetable(str(csv_path))
    print(f"   CSV数据加载结果: {len(lifetable_data_csv)} 个性别组")
    
    # 3. 在模型中使用自定义生命表
    print("\n3. 在模型中使用自定义生命表...")
    model = ColorectalCancerMicrosimulationModel(initial_population=1000)
    
    # 加载自定义生命表
    success = model.load_custom_life_table(str(excel_path))
    if success:
        print("   ✅ 模型成功加载自定义生命表")
    else:
        print("   ❌ 模型加载自定义生命表失败")
    
    # 4. 设置人口并运行简单模拟
    print("\n4. 运行模拟测试...")
    age_distribution = {50: 0.3, 60: 0.4, 70: 0.3}
    model.setup_population(age_distribution, gender_ratio=0.5)
    
    # 运行短期模拟测试
    results = model.run_simulation(years=5, screening_strategy="annual_fit", show_progress=False)
    
    print("   ✅ 模拟完成")
    print(f"   📊 最终存活人口: {results['population_outcome']['final_population']}")
    
    # 5. 比较默认生命表和自定义生命表的差异
    print("\n5. 比较生命表数据...")
    
    # 创建使用默认生命表的模型
    default_model = ColorectalCancerMicrosimulationModel(initial_population=1000)
    default_model.setup_population(age_distribution, gender_ratio=0.5)
    default_results = default_model.run_simulation(years=5, screening_strategy="annual_fit", show_progress=False)
    
    print("   📊 生命表比较结果:")
    print(f"      - 默认生命表最终人口: {default_results['population_outcome']['final_population']}")
    print(f"      - 自定义生命表最终人口: {results['population_outcome']['final_population']}")
    
    # 6. 展示如何处理错误情况
    print("\n6. 错误处理演示...")
    
    # 尝试加载不存在的文件
    print("   测试不存在的文件:")
    success = model.load_custom_life_table("nonexistent_file.xlsx")
    print(f"   结果: {'成功' if success else '失败（预期）'}")
    
    # 尝试加载不支持的格式
    print("   测试不支持的格式:")
    try:
        data_loader.load_custom_lifetable("test.txt")
    except Exception as e:
        print(f"   捕获异常: {type(e).__name__}")
    
    print("\n=== 示例完成 ===")
    print("💡 提示:")
    print("   - 支持的文件格式: .xlsx, .xls, .csv")
    print("   - 支持的列名: age/年龄, male/男, female/女, total/总计")
    print("   - 数据会自动缓存，避免重复加载")
    print("   - 可以在模拟运行前的任何时候加载自定义生命表")


def show_lifetable_format_requirements():
    """展示生命表文件格式要求"""
    print("\n=== 生命表文件格式要求 ===")
    print("支持的文件格式: .xlsx, .xls, .csv")
    print("\n必需的列:")
    print("1. 年龄列 (必需)")
    print("   - 支持的列名: 'age', '年龄', 'Age', 'x'")
    print("   - 数据类型: 整数 (0-100)")
    
    print("\n2. 死亡率列 (至少一列)")
    print("   - 男性死亡率: 'male', '男', 'qx_m', 'mx_m'")
    print("   - 女性死亡率: 'female', '女', 'qx_f', 'mx_f'")
    print("   - 总体死亡率: 'total', 'qx', 'mx', '死亡率'")
    print("   - 数据类型: 浮点数 (0.0-1.0)")
    
    print("\n示例数据格式:")
    sample_data = pd.DataFrame({
        'age': [0, 1, 5, 10, 15, 20],
        'male_mortality': [0.007, 0.0005, 0.0002, 0.0002, 0.0008, 0.001],
        'female_mortality': [0.006, 0.0004, 0.0001, 0.0001, 0.0003, 0.0005],
        'total_mortality': [0.0065, 0.00045, 0.00015, 0.00015, 0.00055, 0.00075]
    })
    print(sample_data.to_string(index=False))


if __name__ == '__main__':
    demonstrate_lifetable_loading()
    show_lifetable_format_requirements()
