# PowerShell 激活脚本
Write-Host "正在激活虚拟环境..." -ForegroundColor Green

# 激活虚拟环境
& .\.env\Scripts\Activate.ps1

Write-Host "虚拟环境已激活！" -ForegroundColor Green
Write-Host "您现在可以运行项目代码了。" -ForegroundColor Yellow
Write-Host ""
Write-Host "常用命令：" -ForegroundColor Cyan
Write-Host "  python simple_test.py                    - 运行简单测试" -ForegroundColor White
Write-Host "  python examples\basic_simulation.py      - 运行基本模拟示例" -ForegroundColor White
Write-Host "  python examples\data_integrated_simulation.py - 运行数据集成模拟" -ForegroundColor White
Write-Host "  python -m pytest                        - 运行所有测试" -ForegroundColor White
Write-Host "  pip list                                 - 查看已安装的包" -ForegroundColor White
Write-Host ""
