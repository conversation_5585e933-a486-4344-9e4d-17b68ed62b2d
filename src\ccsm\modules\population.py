"""
人口模块
实现动态衰减人口模拟，包括人口初始化、更新和统计功能
"""

import numpy as np
import random
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from ..core.enums import Gender, DeathCause, SimulationType
from ..core.individual import Individual
from ..data.data_loader import DataLoader


@dataclass
class PopulationParameters:
    """人口参数配置"""
    max_age: int = 100                    # 最大年龄
    start_year: int = 2020               # 模拟开始年份
    simulation_type: SimulationType = SimulationType.NATURAL_POPULATION  # 模拟类型


class LifeTable:
    """生命表类"""

    def __init__(self):
        self.mortality_rates = self._load_default_life_table()

    def _load_default_life_table(self) -> Dict[Tuple[Gender, int], float]:
        """加载默认生命表数据（基于中国人口数据）"""
        life_table = {}

        # 男性死亡率（年龄别）
        male_mortality = [
            0.001, 0.0005, 0.0002, 0.0001, 0.0001, 0.0001, 0.0001, 0.0002, 0.0003, 0.0005,  # 0-9
            0.001, 0.002, 0.003, 0.004, 0.005, 0.007, 0.01, 0.015, 0.02, 0.025,            # 10-19
            0.03, 0.035, 0.04, 0.045, 0.05, 0.055, 0.06, 0.065, 0.07, 0.075,              # 20-29
            0.08, 0.085, 0.09, 0.095, 0.10, 0.11, 0.12, 0.13, 0.14, 0.15,                 # 30-39
            0.16, 0.17, 0.18, 0.19, 0.20, 0.21, 0.22, 0.23, 0.24, 0.25,                   # 40-49
            0.26, 0.27, 0.28, 0.29, 0.30, 0.31, 0.32, 0.33, 0.34, 0.35,                   # 50-59
            0.36, 0.37, 0.38, 0.39, 0.40, 0.41, 0.42, 0.43, 0.44, 0.45,                   # 60-69
            0.46, 0.47, 0.48, 0.49, 0.50, 0.51, 0.52, 0.53, 0.54, 0.55,                   # 70-79
            0.56, 0.57, 0.58, 0.59, 0.60, 0.61, 0.62, 0.63, 0.64, 0.65,                   # 80-89
            0.66, 0.67, 0.68, 0.69, 0.70, 0.71, 0.72, 0.73, 0.74, 0.75                    # 90-99
        ]

        # 女性死亡率（年龄别）
        female_mortality = [
            0.0008, 0.0004, 0.00015, 0.00008, 0.00008, 0.00008, 0.00008, 0.00015, 0.00025, 0.0004,  # 0-9
            0.0008, 0.0015, 0.0025, 0.0035, 0.0045, 0.006, 0.008, 0.012, 0.016, 0.02,              # 10-19
            0.024, 0.028, 0.032, 0.036, 0.04, 0.044, 0.048, 0.052, 0.056, 0.06,                    # 20-29
            0.064, 0.068, 0.072, 0.076, 0.08, 0.085, 0.09, 0.095, 0.10, 0.105,                     # 30-39
            0.11, 0.115, 0.12, 0.125, 0.13, 0.135, 0.14, 0.145, 0.15, 0.155,                       # 40-49
            0.16, 0.165, 0.17, 0.175, 0.18, 0.185, 0.19, 0.195, 0.20, 0.205,                       # 50-59
            0.21, 0.215, 0.22, 0.225, 0.23, 0.235, 0.24, 0.245, 0.25, 0.255,                       # 60-69
            0.26, 0.265, 0.27, 0.275, 0.28, 0.285, 0.29, 0.295, 0.30, 0.305,                       # 70-79
            0.31, 0.315, 0.32, 0.325, 0.33, 0.335, 0.34, 0.345, 0.35, 0.355,                       # 80-89
            0.36, 0.365, 0.37, 0.375, 0.38, 0.385, 0.39, 0.395, 0.40, 0.405                        # 90-99
        ]

        # 构建生命表字典
        for age in range(100):
            male_rate = male_mortality[age] if age < len(male_mortality) else 1.0
            female_rate = female_mortality[age] if age < len(female_mortality) else 1.0

            life_table[(Gender.MALE, age)] = male_rate
            life_table[(Gender.FEMALE, age)] = female_rate

        return life_table

    def get_mortality_rate(self, gender: Gender, age: int) -> float:
        """获取指定性别和年龄的死亡率"""
        if age >= 100:
            return 1.0  # 100岁以上必死

        return self.mortality_rates.get((gender, age), 0.5)

    def load_custom_life_table(self, file_path: str):
        """从文件加载自定义生命表"""
        try:
            # 使用DataLoader加载自定义生命表数据
            from pathlib import Path
            import pandas as pd

            file_path = Path(file_path)

            if not file_path.exists():
                raise FileNotFoundError(f"生命表文件不存在: {file_path}")

            # 根据文件扩展名选择读取方式
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

            # 使用DataLoader的处理方法解析生命表数据
            temp_loader = DataLoader()
            lifetable_data = temp_loader._process_lifetable_data(df)

            # 更新当前生命表的死亡率数据
            for age in range(0, 101):
                # 男性死亡率
                if 'male' in lifetable_data and age in lifetable_data['male']:
                    self.mortality_rates[(Gender.MALE, age)] = lifetable_data['male'][age]

                # 女性死亡率
                if 'female' in lifetable_data and age in lifetable_data['female']:
                    self.mortality_rates[(Gender.FEMALE, age)] = lifetable_data['female'][age]

                # 总体死亡率（如果没有性别分组数据）
                if 'total' in lifetable_data and age in lifetable_data['total']:
                    if (Gender.MALE, age) not in self.mortality_rates:
                        self.mortality_rates[(Gender.MALE, age)] = lifetable_data['total'][age]
                    if (Gender.FEMALE, age) not in self.mortality_rates:
                        self.mortality_rates[(Gender.FEMALE, age)] = lifetable_data['total'][age]

            print(f"✅ 成功加载自定义生命表: {file_path}")
            return True

        except Exception as e:
            print(f"❌ 加载自定义生命表失败: {e}")
            return False


class PopulationModule:
    """人口模块"""

    def __init__(self, initial_population: int, params: Optional[PopulationParameters] = None):
        self.initial_population = initial_population
        self.params = params or PopulationParameters()
        self.life_table = LifeTable()
        self.population: List[Individual] = []
        self.population_history: List[Dict] = []

        # 初始化数据加载器
        self.data_loader = DataLoader()

        # 加载实际数据
        self.lifetable_data = self.data_loader.load_lifetable_data()
        self.population_data = self.data_loader.load_population_data()

        # 更新生命表数据
        self._update_life_table_with_real_data()

    def _update_life_table_with_real_data(self):
        """使用实际数据更新生命表"""
        try:
            # 更新LifeTable对象的死亡率数据
            for age in range(0, 101):
                # 男性死亡率
                if age in self.lifetable_data['male']:
                    self.life_table.mortality_rates[(Gender.MALE, age)] = self.lifetable_data['male'][age]

                # 女性死亡率
                if age in self.lifetable_data['female']:
                    self.life_table.mortality_rates[(Gender.FEMALE, age)] = self.lifetable_data['female'][age]

            print("✅ 生命表数据已更新为实际数据")

        except Exception as e:
            print(f"⚠️ 更新生命表数据失败，使用默认数据: {e}")

    def load_custom_life_table(self, file_path: str) -> bool:
        """
        为人口模块加载自定义生命表

        Args:
            file_path: 生命表文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            # 使用DataLoader加载自定义生命表
            custom_lifetable_data = self.data_loader.load_custom_lifetable(file_path)

            # 更新LifeTable对象的死亡率数据
            for age in range(0, 101):
                # 男性死亡率
                if 'male' in custom_lifetable_data and age in custom_lifetable_data['male']:
                    self.life_table.mortality_rates[(Gender.MALE, age)] = custom_lifetable_data['male'][age]

                # 女性死亡率
                if 'female' in custom_lifetable_data and age in custom_lifetable_data['female']:
                    self.life_table.mortality_rates[(Gender.FEMALE, age)] = custom_lifetable_data['female'][age]

                # 总体死亡率（如果没有性别分组数据）
                if 'total' in custom_lifetable_data and age in custom_lifetable_data['total']:
                    if (Gender.MALE, age) not in self.life_table.mortality_rates:
                        self.life_table.mortality_rates[(Gender.MALE, age)] = custom_lifetable_data['total'][age]
                    if (Gender.FEMALE, age) not in self.life_table.mortality_rates:
                        self.life_table.mortality_rates[(Gender.FEMALE, age)] = custom_lifetable_data['total'][age]

            # 更新缓存的生命表数据
            self.lifetable_data = custom_lifetable_data

            print(f"✅ 人口模块已更新为自定义生命表数据")
            return True

        except Exception as e:
            print(f"❌ 人口模块加载自定义生命表失败: {e}")
            return False

    def initialize_population(self, age_distribution: Optional[Dict[int, float]] = None,
                            gender_ratio: Optional[float] = None) -> List[Individual]:
        """
        初始化人口队列

        Args:
            age_distribution: 年龄分布字典 {年龄: 比例}，如果为None则使用实际数据
            gender_ratio: 男性比例 (0-1)，如果为None则使用实际数据

        Returns:
            初始化的个体列表
        """
        population = []

        # 使用实际数据或提供的参数
        if age_distribution is None:
            age_distribution = self.population_data['age_distribution']
            print("✅ 使用南山区实际年龄分布数据")

        if gender_ratio is None:
            gender_ratio = self.population_data['gender_ratio']
            print(f"✅ 使用南山区实际性别比例: {gender_ratio:.3f}")

        # 验证年龄分布
        if abs(sum(age_distribution.values()) - 1.0) > 0.001:
            raise ValueError("年龄分布比例之和必须等于1")

        for i in range(self.initial_population):
            # 随机生成性别
            gender = Gender.MALE if random.random() < gender_ratio else Gender.FEMALE

            # 根据年龄分布随机生成年龄
            ages = list(age_distribution.keys())
            age_probs = list(age_distribution.values())
            age = np.random.choice(ages, p=age_probs)

            # 创建个体
            individual = Individual(
                id=i,
                gender=gender,
                birth_year=self.params.start_year - age,
                current_age=age
            )

            population.append(individual)

        self.population = population
        return population

    def update_population(self, current_year: int) -> List[Individual]:
        """
        更新人口状态（年龄增长和自然死亡）

        Args:
            current_year: 当前年份

        Returns:
            存活的个体列表
        """
        alive_population = []
        deaths_this_year = {'natural': 0, 'cancer': 0, 'other': 0}

        for individual in self.population:
            if not individual.alive:
                # 统计已死亡个体
                if individual.death_cause == DeathCause.NATURAL:
                    deaths_this_year['natural'] += 1
                elif individual.death_cause == DeathCause.CANCER:
                    deaths_this_year['cancer'] += 1
                else:
                    deaths_this_year['other'] += 1
                continue

            # 更新年龄
            age = current_year - individual.birth_year
            individual.current_age = age

            # 检查是否超过最大年龄
            if age >= self.params.max_age:
                individual.set_death(current_year, DeathCause.NATURAL)
                deaths_this_year['natural'] += 1
                continue

            # 根据生命表计算自然死亡概率
            death_prob = self.life_table.get_mortality_rate(individual.gender, age)

            if random.random() < death_prob:
                individual.set_death(current_year, DeathCause.NATURAL)
                deaths_this_year['natural'] += 1
            else:
                alive_population.append(individual)

        # 更新人口列表
        self.population = alive_population + [ind for ind in self.population if not ind.alive]

        # 记录人口历史
        self._record_population_history(current_year, deaths_this_year)

        return alive_population


    def _record_population_history(self, year: int, deaths: Dict[str, int]):
        """记录人口历史数据"""
        alive_count = len([ind for ind in self.population if ind.alive])

        history_record = {
            'year': year,
            'alive_population': alive_count,
            'total_population': len(self.population),
            'deaths_natural': deaths['natural'],
            'deaths_cancer': deaths['cancer'],
            'deaths_other': deaths['other'],
            'age_distribution': self._get_age_distribution(),
            'gender_distribution': self._get_gender_distribution()
        }

        self.population_history.append(history_record)

    def _get_age_distribution(self) -> Dict[str, int]:
        """获取当前人口年龄分布"""
        age_groups = {
            '50-54': 0, '55-59': 0, '60-64': 0, '65-69': 0,
            '70-74': 0, '75-79': 0, '80-84': 0, '85+': 0
        }

        for individual in self.population:
            if not individual.alive:
                continue

            age = individual.current_age
            if 50 <= age < 55:
                age_groups['50-54'] += 1
            elif 55 <= age < 60:
                age_groups['55-59'] += 1
            elif 60 <= age < 65:
                age_groups['60-64'] += 1
            elif 65 <= age < 70:
                age_groups['65-69'] += 1
            elif 70 <= age < 75:
                age_groups['70-74'] += 1
            elif 75 <= age < 80:
                age_groups['75-79'] += 1
            elif 80 <= age < 85:
                age_groups['80-84'] += 1
            elif age >= 85:
                age_groups['85+'] += 1

        return age_groups

    def _get_gender_distribution(self) -> Dict[str, int]:
        """获取当前人口性别分布"""
        gender_dist = {'male': 0, 'female': 0}

        for individual in self.population:
            if not individual.alive:
                continue

            if individual.gender == Gender.MALE:
                gender_dist['male'] += 1
            else:
                gender_dist['female'] += 1

        return gender_dist

    def get_population_statistics(self) -> Dict:
        """获取人口统计信息"""
        alive_population = [ind for ind in self.population if ind.alive]
        dead_population = [ind for ind in self.population if not ind.alive]

        stats = {
            'total_population': len(self.population),
            'alive_population': len(alive_population),
            'dead_population': len(dead_population),
            'mortality_rate': len(dead_population) / len(self.population) if self.population else 0,
            'age_distribution': self._get_age_distribution(),
            'gender_distribution': self._get_gender_distribution(),
            'death_causes': {
                'natural': len([ind for ind in dead_population if ind.death_cause == DeathCause.NATURAL]),
                'cancer': len([ind for ind in dead_population if ind.death_cause == DeathCause.CANCER]),
                'other': len([ind for ind in dead_population if ind.death_cause == DeathCause.OTHER])
            }
        }

        if alive_population:
            ages = [ind.current_age for ind in alive_population]
            stats['mean_age'] = np.mean(ages)
            stats['median_age'] = np.median(ages)
            stats['age_range'] = (min(ages), max(ages))

        return stats

    def get_eligible_population(self, year: int, min_age: int = 50, max_age: int = 75) -> List[Individual]:
        """获取符合筛查条件的人群"""
        eligible = []

        for individual in self.population:
            if individual.is_eligible_for_screening(year, min_age, max_age):
                eligible.append(individual)

        return eligible

    def create_birth_cohort(self, birth_year: int, cohort_size: int,
                          gender_ratio: float = 0.5) -> List[Individual]:
        """
        创建出生队列（用于出生队列模拟）

        Args:
            birth_year: 出生年份
            cohort_size: 队列大小
            gender_ratio: 男性比例

        Returns:
            出生队列个体列表
        """
        cohort = []

        for i in range(cohort_size):
            gender = Gender.MALE if random.random() < gender_ratio else Gender.FEMALE

            individual = Individual(
                id=len(self.population) + i,  # 避免ID冲突
                gender=gender,
                birth_year=birth_year,
                current_age=0
            )

            cohort.append(individual)

        # 添加到总人口中
        self.population.extend(cohort)

        return cohort

    def simulate_population_growth(self, years: int, annual_birth_rate: float = 0.01):
        """
        模拟人口增长（可选功能，用于特殊场景）

        Args:
            years: 模拟年数
            annual_birth_rate: 年度出生率
        """
        # 这是一个简化的人口增长模型
        # 实际应用中可能需要更复杂的人口动力学模型
        for year in range(years):
            current_year = self.params.start_year + year
            alive_pop = len([ind for ind in self.population if ind.alive])

            # 计算新生儿数量
            new_births = int(alive_pop * annual_birth_rate)

            if new_births > 0:
                new_cohort = self.create_birth_cohort(current_year, new_births)
                print(f"年份 {current_year}: 新增 {new_births} 个新生儿")

    def export_population_data(self, file_path: str, format: str = 'csv'):
        """
        导出人口数据

        Args:
            file_path: 导出文件路径
            format: 导出格式 ('csv', 'excel', 'json')
        """
        import pandas as pd

        # 准备导出数据
        data = []
        for individual in self.population:
            data.append(individual.to_dict())

        df = pd.DataFrame(data)

        if format.lower() == 'csv':
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif format.lower() == 'excel':
            df.to_excel(file_path, index=False)
        elif format.lower() == 'json':
            df.to_json(file_path, orient='records', force_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

        print(f"人口数据已导出到: {file_path}")

    def get_population_history(self) -> List[Dict]:
        """获取人口历史数据"""
        return self.population_history

    def reset_population(self):
        """重置人口数据"""
        self.population = []
        self.population_history = []